#!/usr/bin/env python3
"""
🧪 MOCK FILE GENERATION TESTER

Creates mock profitable patterns that match <PERSON><PERSON><PERSON>'s output format
and tests all file generation functionality without running full pattern discovery.

This saves time by focusing on file generation testing with realistic mock data.
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import MagicMock

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from file_generator import FileGenerator
from backtesting_rule_parser import TradingPattern
import config

def create_mock_profitable_pattern():
    """Create a realistic mock profitable ORB pattern"""
    
    pattern = TradingPattern(
        pattern_name="London Session 30-Min ORB Breakout",
        description="Opening Range Breakout pattern targeting London session volatility with 30-minute range definition",
        market_situation="High volatility London session opening with clear directional bias",
        entry_conditions=[
            {
                "condition": "orb_breakout_above",
                "orb_period_minutes": 30,
                "session": "london",
                "breakout_threshold": 0.0005
            },
            {
                "condition": "session_filter", 
                "session": "london",
                "start_hour": 9,
                "end_hour": 17
            }
        ],
        exit_conditions=[
            {
                "condition": "risk_reward_ratio",
                "risk": 1,
                "reward": 2.5
            },
            {
                "condition": "session_end",
                "session": "london",
                "minutes_before_end": 30
            }
        ],
        entry_logic="AND",
        filters=[
            {
                "condition": "orb_range_size",
                "min_range_pips": 10,
                "max_range_pips": 50
            }
        ],
        position_sizing={
            "method": "fixed_percent",
            "value": 0.02
        },
        behavioral_logic="Institutional traders create volatility at London open, retail follows breakouts",
        statistical_edge={
            "win_rate": 68.5,
            "avg_win": 2.3,
            "avg_loss": -1.0,
            "profit_factor": 1.85
        },
        optimal_conditions={
            "timeframes": ["5min"],
            "sessions": ["london"],
            "market_regime": ["trending", "volatile"]
        },
        implementation_notes="Use limit orders 2 pips above/below ORB high/low for better fills"
    )
    
    return pattern

def create_mock_backtest_results():
    """Create realistic mock backtesting results with backtesting.py stats format"""

    # Create mock backtesting.py stats object that mimics the real stats format
    mock_stats = MagicMock()

    # Set up the stats data
    stats_data = {
        'Return [%]': 24.8,
        'Return (Ann.) [%]': 31.2,
        'Max. Drawdown [%]': -12.3,
        'Sharpe Ratio': 1.68,
        '# Trades': 47,
        'Win Rate [%]': 68.5,
        'Start': 100000,
        'Equity Final [$]': 124800,
        'Best Trade [%]': 8.2,
        'Worst Trade [%]': -3.1,
        'Avg. Trade [%]': 0.53,
        'Max. Trade Duration': '2 days 04:30:00',
        'Avg. Trade Duration': '0 days 08:15:00',
        'Profit Factor': 1.85,
        'SQN': 2.1,
        'Calmar Ratio': 2.54,
        'Volatility (Ann.) [%]': 18.6
    }

    # Create sample trade data first
    trade_data = []
    base_date = datetime(2024, 5, 27, 9, 0)  # London session start

    for i in range(47):  # 47 trades as per stats
        entry_time = base_date + timedelta(days=i*2, hours=i%8)
        exit_time = entry_time + timedelta(hours=8, minutes=15)  # Avg duration

        # Simulate realistic trade outcomes (68.5% win rate)
        is_winner = i < 32  # First 32 trades are winners (68.5% of 47)

        if is_winner:
            profit_pct = 0.023 * (1 + (i % 3) * 0.5)  # Varying wins around 2.3%
            profit = profit_pct * 100000 * 0.02  # 2% position size
        else:
            profit_pct = -0.01 * (1 + (i % 2) * 0.5)  # Varying losses around -1%
            profit = profit_pct * 100000 * 0.02

        entry_price = 18700 + (i * 10) + (i % 50)  # Realistic DAX prices
        exit_price = entry_price * (1 + profit_pct)

        trade_data.append({
            'EntryTime': entry_time,
            'ExitTime': exit_time,
            'Direction': 'Long',
            'EntryPrice': entry_price,
            'ExitPrice': exit_price,
            'Profit': profit,
            'Size': 5,  # 5 lots
            'StopLoss': entry_price * 0.999,  # 1% stop
            'TakeProfit': entry_price * 1.025,  # 2.5% target
            'EntryBar': i * 10,
            'ExitBar': i * 10 + 5,
            'ReturnPct': profit_pct,
            'Duration': exit_time - entry_time,
            'Tag': f'ORB_Pattern_1'
        })

    trades_df = pd.DataFrame(trade_data)

    # Configure the mock to behave like backtesting.py stats
    mock_stats.get.side_effect = lambda key, default=0: stats_data.get(key, default)
    mock_stats.empty = False  # Ensure it's not considered empty

    # Add _trades attribute for CSV generation
    mock_stats._trades = trades_df
    
    backtest_results = [{
        'backtesting_py_stats': mock_stats,
        'trades_df': trades_df,
        'pattern_id': 1,
        'pattern_text': '{"pattern_name": "London Session 30-Min ORB Breakout", "entry_conditions": [{"condition": "orb_breakout_above"}]}',
        'is_profitable': True,
        'return_pct': 24.8,
        'bt_result': mock_stats  # Add bt_result for CSV generation
    }]
    
    return backtest_results

def create_mock_validation_results(profitable_pattern):
    """Create mock walk-forward validation results"""
    
    validation_results = {
        'success': True,
        'total_patterns': 1,
        'profitable_patterns': [profitable_pattern],
        'success_rate': 100.0,
        'validation_results': {
            1: {
                'metrics': {
                    'avg_return': 24.8,
                    'avg_win_rate': 68.5,
                    'max_drawdown': -12.3,
                    'total_trades': 47,
                    'profit_factor': 1.85,
                    'sharpe_ratio': 1.68
                }
            }
        }
    }
    
    return validation_results

def create_mock_cortex_results():
    """Create mock Cortex LLM analysis results"""
    
    # Create sample OHLC data
    dates = pd.date_range(start='2024-05-27', end='2024-06-30', freq='5min')
    ohlc_data = pd.DataFrame({
        'Open': 18700 + pd.Series(range(len(dates))) * 0.1 + pd.Series(range(len(dates))).apply(lambda x: (x % 100) * 0.5),
        'High': 18705 + pd.Series(range(len(dates))) * 0.1 + pd.Series(range(len(dates))).apply(lambda x: (x % 100) * 0.6),
        'Low': 18695 + pd.Series(range(len(dates))) * 0.1 + pd.Series(range(len(dates))).apply(lambda x: (x % 100) * 0.4),
        'Close': 18700 + pd.Series(range(len(dates))) * 0.1 + pd.Series(range(len(dates))).apply(lambda x: (x % 100) * 0.45),
        'Volume': 1000 + pd.Series(range(len(dates))).apply(lambda x: (x % 500) * 2)
    }, index=dates)
    
    llm_analysis = """
## 🎯 PATTERN DISCOVERY ANALYSIS

### London Session ORB Strategy

**Market Psychology**: The London session opening creates institutional volatility as European markets come online. Large players establish positions, creating clear directional moves that retail traders can follow through Opening Range Breakouts.

**Statistical Edge**: 
- 68.5% win rate with 2.5:1 risk-reward ratio
- Profit factor of 1.85 indicates strong edge
- Average trade duration of 8 hours 15 minutes allows for proper trend development

**Entry Logic**: 
- Wait for 30-minute opening range establishment (9:00-9:30 London time)
- Enter long on breakout above range high with 2-pip buffer
- Confirm breakout with volume expansion and momentum

**Risk Management**:
- Stop loss at opening range low minus 5 pips
- Take profit at 2.5x risk distance
- Exit all positions 30 minutes before London session close

**Optimal Conditions**:
- High volatility London sessions
- Clear trending market regime
- 5-minute timeframe for precise entries
- Avoid major news events during first 30 minutes
"""
    
    cortex_results = {
        'symbol': 'DEUIDXEUR',
        'llm_analysis': llm_analysis,
        'mt4_ea_code': '// Mock MT4 EA Code\n// Generated by Jaeger Test',
        'ea_name': 'Jaeger_DEUIDXEUR_Test',
        'ohlc_data': ohlc_data,
        'patterns': ['London Session 30-Min ORB Breakout']
    }
    
    return cortex_results

def test_file_generation():
    """Test complete file generation workflow with mock data"""

    print("🧪 TESTING FILE GENERATION WITH MOCK DATA")
    print("=" * 50)

    # Test 1: Single Pattern
    print("\n📊 TEST 1: SINGLE PROFITABLE PATTERN")
    print("-" * 40)

    # Create mock data
    print("📊 Creating mock profitable pattern...")
    profitable_pattern = create_mock_profitable_pattern()

    print("📈 Creating mock backtest results...")
    backtest_results = create_mock_backtest_results()

    print("✅ Creating mock validation results...")
    validation_results = create_mock_validation_results(profitable_pattern)

    print("🧠 Creating mock Cortex results...")
    cortex_results = create_mock_cortex_results()

    # Test file generation
    print("\n📁 TESTING FILE GENERATION...")
    print("-" * 30)

    generator = FileGenerator()

    try:
        generated_files = generator.generate_trading_system_files(
            cortex_results=cortex_results,
            backtest_results=backtest_results,
            validation_results=validation_results
        )

        print(f"\n✅ FILE GENERATION SUCCESSFUL!")
        print(f"📂 System folder: {generated_files['system_folder']}")
        print(f"📄 Files generated:")

        for file_type, file_path in generated_files.items():
            if file_type not in ['system_folder', 'timestamp']:
                if isinstance(file_path, list):
                    print(f"   • {file_type}: {len(file_path)} files")
                    for fp in file_path:
                        if fp:
                            print(f"     - {os.path.basename(fp)}")
                else:
                    if file_path:
                        print(f"   • {file_type}: {os.path.basename(file_path)}")

        # Verify files exist and validate content
        print(f"\n🔍 VERIFYING GENERATED FILES...")
        system_folder = generated_files['system_folder']

        if os.path.exists(system_folder):
            files_in_folder = os.listdir(system_folder)
            print(f"📁 Files in {os.path.basename(system_folder)}:")

            required_files = {
                '.md': False,    # Trading system report
                '.mq4': False,   # MT4 EA
                '.html': False,  # Interactive chart
                '.csv': False    # Trade data
            }

            for file in sorted(files_in_folder):
                file_path = os.path.join(system_folder, file)
                file_size = os.path.getsize(file_path)
                print(f"   • {file} ({file_size:,} bytes)")

                # Check if required file types are present
                for ext in required_files:
                    if file.endswith(ext):
                        required_files[ext] = True

            # Validate all required files are present
            missing_files = [ext for ext, present in required_files.items() if not present]
            if missing_files:
                print(f"   ⚠️ Missing file types: {missing_files}")
                return False
            else:
                print(f"   ✅ All required file types present!")

        return True

    except Exception as e:
        print(f"\n❌ FILE GENERATION FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_file_generation()
    
    if success:
        print(f"\n🎉 MOCK FILE GENERATION TEST COMPLETED SUCCESSFULLY!")
        print(f"📁 Check the 'results' folder for generated files")
    else:
        print(f"\n💥 MOCK FILE GENERATION TEST FAILED!")
        sys.exit(1)
